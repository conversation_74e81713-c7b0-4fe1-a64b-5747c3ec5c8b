<template>
	<view class="static">
		<uni-nav-bar status-bar :fixed="true" :border="false">
			<block v-slot:left>
				<uni-icons @click="navigateBack()" type="left" size="30"></uni-icons>
			</block>
			<view class="flex fontSize-34  w-full  justify-center items-center">
				<text v-text="title"></text>
			</view>
			<block v-if="rightText" v-slot:right>
				<view @click="navigateToSecondPage()" class="flex fontSize-28  w-full justify-center items-center">
					<text v-text="rightText"></text>
				</view>

				<!-- @click="navigateToUrl(`${rightUrl}`)"	<uni-icons @click="toggleMenu()" type="more-filled" size="30"></uni-icons> -->
			</block>
		</uni-nav-bar>
		<view class="flex absolute" style="width: 100%;"
			:style="{height: `calc(100vh - ${navBarHeight}px)`,top:`${navBarHeight}px`}">
			<web-view @message="handleWebViewMessage" @onPostMessage="handleWebViewMessage"
				@load="onWebViewLoad" @error="onWebViewError" :webview-styles="webviewStyles" :fullscreen="false"
				style="height:100%;width:100%" :src='webViewUrl'></web-view>
		</view>
	</view>
</template>

<script setup>
// React 前端项目可以继续使用原有的 Inherit.js 基类，无需修改任何代码，实现了最大程度的兼容性。
	import {
		initNFC,
		getNfcText,
		closeNFC,
		setNfcType,
		setNfcText
	} from '@/utils/nfcSoft.js';
	import {
		ref,
		computed,
		onMounted,
		getCurrentInstance
	} from 'vue';
	const NFCTimer =ref(null)
	const title = ref("")
	const rightText = ref("") //使用 rightText 属性设置导航栏右侧文字
	const rightUrl = ref("") //使用 rightUrl 属性设置导航栏右侧地址
	const defaultPageTitle = ref("")
	const navBarHeight = ref(44) // 顶部标题高度https://www.baidu.com
	const tabBarHeight = ref(50) // 底部菜单栏高度 
	const otherHeight = ref(0)
	//const webViewUrl = ref('http://**************:19719/subapp/customform/designview?formName=265809164288069&retrunPath=designlist') //https://www.baidu.com http://localhost:5173/#/FirstMaterialDetail
	const webViewUrl = ref('')
	const originalUrl = 'http://**************:19719/subapp/PCBSFC/?/App/PrintInk/Dryfilm'
	const reloadWebViewUrl= ref('')
	const systemInfo = uni.getSystemInfoSync(); //系统信息

	const windowHeight = computed(() => {
		return systemInfo.windowHeight
	})
	const webviewStyles = ref({
		top: (navBarHeight.value + systemInfo.safeArea.top) + 'px', //  非自定义页面，不需要顶部菜单栏+安全栏 位置
		width: '100%',
		height: (windowHeight.value - navBarHeight.value - systemInfo.safeArea.top) + 'px',
		progress: {
			color: '#00ffff'
		}
	})
	onMounted(() => {

		const instance = getCurrentInstance().proxy
		const eventChannel = instance.getOpenerEventChannel();
		// 监听acceptDataFromOpenerPage事件，获取上一页面通过eventChannel传送到当前页面的数据
		eventChannel.on('acceptDataFromOpenerPage', function(data) {
			console.log('acceptDataFromOpenerPage', JSON.stringify(data))
			if (data.text) {
				// 设置当前页面标题
				title.value = data.text
				// debugger
				defaultPageTitle.value = data.text
				uni.setNavigationBarTitle({
					title: data.text,
					success: function() {
						console.log('Navigation bar title set successfully.');
					},
					fail: function(err) {
						console.error('Failed to set navigation bar title.', err);
					}
				});
			}
		})

		 // 新增：注册全局函数以兼容 React 版本
    	registerGlobalFunctions()

    	// 立即设置兼容性，确保 ReactNativeWebView 对象可用
    	setupWebViewCompatibility()

    	// 设置 webview URL
    	setupWebViewUrl()

    	// 立即尝试注入脚本
    	setTimeout(() => {
    		injectWebViewScript();
    	}, 100);

    	// 监听来自 webview 的 postMessage (使用 uni-app 的方式)
    	try {
    		if (typeof window !== 'undefined' && window.addEventListener) {
    			window.addEventListener('message', function(event) {
    				console.log('Received message from webview:', event);
    				if (event.data && event.data.type === 'webview-message') {
    					handleWebViewMessage({
    						detail: { data: [event.data.data] }
    					});
    				}
    			});
    		}
    	} catch (error) {
    		console.log('window.addEventListener not available:', error);
    	}
	})

	// 注册全局函数，兼容 React 版本的 Inherit.js
	function registerGlobalFunctions() {
		console.log('注册全局函数，兼容 React 版本的 Inherit.js',window)

		// 确保 window 对象存在
		if (typeof window === 'undefined') {
			window = {};
		}

		// 模拟 window.setCallback
		window.setCallback = function(callback) {
			window.callback = callback;
		}

		// 模拟接收IP的回调
		window.ReciveIp = function(ip) {
			if (window.callback?.ReciveIp) {
				window.callback.ReciveIp(ip);
			}
		}

		// 模拟接收用户数据的回调
		window.BackUser = function(user) {
			if (window.callback?.BackUser) {
				window.callback.BackUser(user);
			}
		}

		// 模拟接收HttpHeader信息的回调
		window.Readinfor = function(information) {
			if (window.callback?.Readinfor) {
				window.callback.Readinfor(information);
			}
		}

		// 模拟右侧按钮点击
		window.PressRight = function() {
			if (window.callback?.PressRight) {
				window.callback.PressRight();
			}
		}

		// 模拟键盘事件
		window.KeyboardDidShow = function() {
			if (window.callback?.KeyboardDidShow) {
				window.callback.KeyboardDidShow();
			}
		}

		window.KeyboardDidHide = function() {
			if (window.callback?.KeyboardDidHide) {
				window.callback.KeyboardDidHide();
			}
		}

		// 模拟接收参数
		window.ReceiveParams = function(params) {
			if (window.callback?.ReceiveParams) {
				window.callback.ReceiveParams(params);
			}
		}

		// 创建兼容的 postMessage 函数
		const compatiblePostMessage = function(message) {
			try {
				console.log('Compatible ReactNativeWebView.postMessage called with:', message);
				// 将消息转发给 uni-app 的消息处理
				const data = JSON.parse(JSON.parse(message));
				handleWebViewMessage({
					detail: { data: [data] }
				});
			} catch (error) {
				console.error('Error in compatible ReactNativeWebView.postMessage:', error);
			}
		};

		// 智能处理 ReactNativeWebView 对象
		try {
			if (!window.ReactNativeWebView) {
				// 如果不存在，直接创建
				window.ReactNativeWebView = {
					postMessage: compatiblePostMessage
				};
				console.log('Created new ReactNativeWebView object');
			} else {
				// 如果已存在，尝试替换 postMessage 方法
				console.log('ReactNativeWebView already exists, attempting to fix postMessage');
				try {
					window.ReactNativeWebView.postMessage = compatiblePostMessage;
					console.log('Successfully replaced postMessage method');
				} catch (readOnlyError) {
					console.log('postMessage is read-only, trying alternative approaches');

					// 创建一个代理对象
					const originalObj = window.ReactNativeWebView;
					const proxyObj = {
						postMessage: compatiblePostMessage,
						// 保留原有的其他属性
						...originalObj
					};

					try {
						// 尝试删除并重新创建
						delete window.ReactNativeWebView;
						window.ReactNativeWebView = proxyObj;
						console.log('Successfully recreated ReactNativeWebView with proxy');
					} catch (deleteError) {
						console.log('Cannot delete ReactNativeWebView, using global backup');
						// 在全局作用域创建备用对象
						window.__BACKUP_ReactNativeWebView = proxyObj;
					}
				}
			}
		} catch (error) {
			console.error('Error setting up ReactNativeWebView in uni-app context:', error);
		}

		// 确保在全局作用域中也可用
		try {
			if (typeof globalThis !== 'undefined') {
				globalThis.ReactNativeWebView = window.ReactNativeWebView || window.__BACKUP_ReactNativeWebView;
			}
			if (typeof global !== 'undefined') {
				global.ReactNativeWebView = window.ReactNativeWebView || window.__BACKUP_ReactNativeWebView;
			}
		} catch (error) {
			console.log('Error setting global ReactNativeWebView:', error);
		}

		console.log('ReactNativeWebView setup completed:', window.ReactNativeWebView);

		// 模拟 iOS webkit 消息处理
		if (!window.webkit) {
			window.webkit = {
				messageHandlers: {
					ReactNativeWebView: {
						postMessage: function(obj) {
							try {
								console.log('webkit.messageHandlers.ReactNativeWebView.postMessage called with:', obj);
								handleWebViewMessage({
									detail: { data: [obj] }
								});
							} catch (error) {
								console.error('Error in webkit postMessage:', error);
							}
						}
					}
				}
			}
		}

		// 向 webview 注入兼容性代码
		//injectCompatibilityScript();
	}

	// 设置 webview URL
	function setupWebViewUrl() {
		// 直接设置原始 URL，让 webview 加载
		webViewUrl.value = originalUrl;
		console.log('WebView URL set to:', webViewUrl.value);
	}

	// webview 加载完成事件
	function onWebViewLoad(event) {
		console.log('WebView loaded, setting up compatibility...', event);

		// 立即注入，不等待
		injectWebViewScript();

		// 延迟一点时间确保webview完全加载
		setTimeout(() => {
			setupWebViewCompatibility();
		}, 100);

		// 多次注入，确保在不同时机都能成功
		setTimeout(() => injectWebViewScript(), 200);
		setTimeout(() => injectWebViewScript(), 500);
		setTimeout(() => injectWebViewScript(), 1000);
		setTimeout(() => injectWebViewScript(), 2000);
		setTimeout(() => injectWebViewScript(), 3000);

		// 持续监控并注入
		const injectionInterval = setInterval(() => {
			injectWebViewScript();
		}, 1000);

		// 10秒后停止持续注入
		setTimeout(() => {
			clearInterval(injectionInterval);
		}, 10000);
	}

	// webview 错误事件
	function onWebViewError(event) {
		console.error('WebView error:', event);
	}

	// 设置 webview 兼容性
	function setupWebViewCompatibility() {
		console.log('Setting up WebView compatibility for React Native...');

		// 确保全局 ReactNativeWebView 对象始终可用
		if (typeof window !== 'undefined') {
			// 创建兼容的 postMessage 函数
			const compatiblePostMessage = function(message) {
				console.log('ReactNativeWebView.postMessage called:', message);
				try {
					// 解析消息并转发给 uni-app 处理
					const data = JSON.parse(JSON.parse(message));
					handleWebViewMessage({
						detail: { data: [data] }
					});
				} catch (error) {
					console.error('Error in ReactNativeWebView.postMessage:', error);
				}
			};

			// 尝试多种方式设置 ReactNativeWebView
			try {
				// 方法1：如果属性不存在，直接创建
				if (!window.ReactNativeWebView) {
					window.ReactNativeWebView = {
						postMessage: compatiblePostMessage
					};
				} else {
					// 方法2：如果已存在，尝试替换 postMessage 方法
					try {
						window.ReactNativeWebView.postMessage = compatiblePostMessage;
					} catch (e) {
						console.log('Cannot replace postMessage directly, trying defineProperty');
						// 方法3：使用 defineProperty 替换
						try {
							Object.defineProperty(window.ReactNativeWebView, 'postMessage', {
								value: compatiblePostMessage,
								writable: true,
								configurable: true
							});
						} catch (e2) {
							console.log('defineProperty on postMessage failed, creating new object');
							// 方法4：删除原有属性并重新创建
							try {
								delete window.ReactNativeWebView;
								window.ReactNativeWebView = {
									postMessage: compatiblePostMessage
								};
							} catch (e3) {
								console.error('All methods to set ReactNativeWebView failed:', e3);
							}
						}
					}
				}
			} catch (error) {
				console.error('Error setting up ReactNativeWebView:', error);
			}

			// iOS webkit 兼容
			Object.defineProperty(window, 'webkit', {
				value: {
					messageHandlers: {
						ReactNativeWebView: {
							postMessage: function(obj) {
								console.log('webkit.messageHandlers.ReactNativeWebView.postMessage called:', obj);
								try {
									handleWebViewMessage({
										detail: { data: [obj] }
									});
								} catch (error) {
									console.error('Error in webkit postMessage:', error);
								}
							}
						}
					}
				},
				writable: false,
				configurable: true
			});

			console.log('WebView compatibility setup completed');
			console.log('window.ReactNativeWebView:', window.ReactNativeWebView);
		}

		// 尝试向 webview 注入脚本
		injectWebViewScript();
	}

	// 向 webview 注入脚本
	function injectWebViewScript() {
		setTimeout(() => {
			try {
				// 尝试获取 webview 实例并注入脚本
				const pages = getCurrentPages();
				if (pages && pages.length > 0) {
					const currentPage = pages[pages.length - 1];
					if (currentPage && currentPage.$getAppWebview) {
						const webview = currentPage.$getAppWebview();
						const webviewChildren = webview.children();
						if (webviewChildren && webviewChildren.length > 0) {
							const targetWebview = webviewChildren[0];

							// 超强力的脚本注入，处理只读属性问题
							const script = `
								(function() {
									console.log('=== SMART ReactNativeWebView compatibility injection ===');
									console.log('Current window.ReactNativeWebView:', window.ReactNativeWebView);
									console.log('ReactNativeWebView type:', typeof window.ReactNativeWebView);

									// 创建一个立即可用的 ReactNativeWebView 对象
									var ReactNativeWebViewObj = {
										postMessage: function(message) {
											console.log('=== INJECTED ReactNativeWebView.postMessage called ===', message);
											console.log('Message type:', typeof message);
											console.log('Message content:', message);

											try {
												// 方案1：使用 uni.postMessage
												if (typeof uni !== 'undefined' && uni.postMessage) {
													console.log('Using uni.postMessage');
													uni.postMessage({
														data: JSON.parse(JSON.parse(message))
													});
													return;
												}
											} catch (e) {
												console.log('uni.postMessage failed, trying parent:', e);
											}

											try {
												// 方案2：使用 parent.postMessage
												if (window.parent && window.parent.postMessage) {
													console.log('Using parent.postMessage');
													window.parent.postMessage({
														type: 'webview-message',
														data: JSON.parse(JSON.parse(message))
													}, '*');
													return;
												}
											} catch (e2) {
												console.log('parent.postMessage failed:', e2);
											}

											try {
												// 方案3：使用 top.postMessage
												if (window.top && window.top.postMessage) {
													console.log('Using top.postMessage');
													window.top.postMessage({
														type: 'webview-message',
														data: JSON.parse(JSON.parse(message))
													}, '*');
													return;
												}
											} catch (e3) {
												console.error('All postMessage methods failed:', e3);
											}
										}
									};

									// 智能处理现有的 ReactNativeWebView 对象
									function handleExistingReactNativeWebView() {
										if (window.ReactNativeWebView) {
											console.log('ReactNativeWebView exists, checking if functional...');

											// 检查 postMessage 是否存在且可用
											if (window.ReactNativeWebView.postMessage && typeof window.ReactNativeWebView.postMessage === 'function') {
												console.log('postMessage exists, testing if it works...');

												// 尝试替换 postMessage 方法
												try {
													var originalPostMessage = window.ReactNativeWebView.postMessage;
													window.ReactNativeWebView.postMessage = ReactNativeWebViewObj.postMessage;
													console.log('Successfully replaced postMessage method');
													return true;
												} catch (readOnlyError) {
													console.log('postMessage is read-only:', readOnlyError.message);
													// 恢复原始方法
													try {
														window.ReactNativeWebView.postMessage = originalPostMessage;
													} catch (e) {}
													return false;
												}
											} else {
												console.log('postMessage does not exist or is not a function');
												return false;
											}
										} else {
											console.log('ReactNativeWebView does not exist');
											return false;
										}
									}

									var existingHandled = handleExistingReactNativeWebView();

									// 在注入前先检查是否已存在
									if (window.ReactNativeWebView) {
										console.log('ReactNativeWebView already exists:', window.ReactNativeWebView);
										console.log('Existing postMessage type:', typeof window.ReactNativeWebView.postMessage);

										// 如果已存在但 postMessage 不可用，尝试修复
										if (!window.ReactNativeWebView.postMessage || typeof window.ReactNativeWebView.postMessage !== 'function') {
											console.log('Existing ReactNativeWebView.postMessage is not functional, fixing...');
										} else {
											console.log('ReactNativeWebView.postMessage exists but may not work, replacing...');
										}
									}

									// 智能设置 ReactNativeWebView 对象
									function setReactNativeWebView() {
										try {
											// 方法1：直接赋值
											window.ReactNativeWebView = ReactNativeWebViewObj;
											return true;
										} catch (e1) {
											console.log('Direct assignment failed:', e1.message);

											try {
												// 方法2：如果是只读属性，尝试替换 postMessage 方法
												if (window.ReactNativeWebView) {
													window.ReactNativeWebView.postMessage = ReactNativeWebViewObj.postMessage;
													return true;
												}
											} catch (e2) {
												console.log('postMessage replacement failed:', e2.message);

												try {
													// 方法3：使用 defineProperty 替换 postMessage
													Object.defineProperty(window.ReactNativeWebView, 'postMessage', {
														value: ReactNativeWebViewObj.postMessage,
														writable: true,
														configurable: true
													});
													return true;
												} catch (e3) {
													console.log('defineProperty on postMessage failed:', e3.message);

													try {
														// 方法4：删除并重新创建
														delete window.ReactNativeWebView;
														window.ReactNativeWebView = ReactNativeWebViewObj;
														return true;
													} catch (e4) {
														console.log('Delete and recreate failed:', e4.message);
														return false;
													}
												}
											}
										}
									}

									// 如果现有对象处理失败，创建新的对象
									if (!existingHandled) {
										console.log('Creating new ReactNativeWebView object...');

										// 尝试多种创建方法
										var creationSuccess = false;

										// 方法1：直接赋值
										try {
											window.ReactNativeWebView = ReactNativeWebViewObj;
											creationSuccess = true;
											console.log('Direct assignment successful');
										} catch (e1) {
											console.log('Direct assignment failed:', e1.message);

											// 方法2：使用 bracket notation
											try {
												window['ReactNativeWebView'] = ReactNativeWebViewObj;
												creationSuccess = true;
												console.log('Bracket notation assignment successful');
											} catch (e2) {
												console.log('Bracket notation failed:', e2.message);

												// 方法3：使用 defineProperty
												try {
													Object.defineProperty(window, 'ReactNativeWebView', {
														value: ReactNativeWebViewObj,
														writable: true,
														configurable: true,
														enumerable: true
													});
													creationSuccess = true;
													console.log('defineProperty successful');
												} catch (e3) {
													console.log('defineProperty failed:', e3.message);
												}
											}
										}

										// 如果所有方法都失败，创建备用对象
										if (!creationSuccess) {
											console.log('All creation methods failed, creating backup object');
											window.__BACKUP_ReactNativeWebView = ReactNativeWebViewObj;

											// 尝试劫持可能的调用
											try {
												var originalError = window.onerror;
												window.onerror = function(message, source, lineno, colno, error) {
													if (message && message.indexOf('ReactNativeWebView') !== -1 && message.indexOf('postMessage') !== -1) {
														console.log('Detected ReactNativeWebView error, using backup');
														// 这里可以尝试执行备用逻辑
													}
													if (originalError) {
														return originalError.apply(this, arguments);
													}
												};
											} catch (e) {
												console.log('Error handler setup failed:', e);
											}
										}
									}

									// 设置到全局作用域作为备用
									try {
										if (typeof globalThis !== 'undefined') {
											globalThis.ReactNativeWebView = window.ReactNativeWebView || window.__BACKUP_ReactNativeWebView;
										}
										if (typeof global !== 'undefined') {
											global.ReactNativeWebView = window.ReactNativeWebView || window.__BACKUP_ReactNativeWebView;
										}
										if (typeof self !== 'undefined') {
											self.ReactNativeWebView = window.ReactNativeWebView || window.__BACKUP_ReactNativeWebView;
										}
									} catch (e) {
										console.log('Global scope assignment failed:', e);
									}

									// iOS webkit 支持
									var webkitObj = {
										messageHandlers: {
											ReactNativeWebView: {
												postMessage: function(obj) {
													console.log('=== WebView webkit postMessage called ===', obj);
													ReactNativeWebViewObj.postMessage(JSON.stringify(JSON.stringify(obj)));
												}
											}
										}
									};

									window.webkit = webkitObj;

									console.log('=== ReactNativeWebView compatibility injected successfully ===');
									console.log('window.ReactNativeWebView:', window.ReactNativeWebView);
									console.log('typeof window.ReactNativeWebView:', typeof window.ReactNativeWebView);
									console.log('typeof window.ReactNativeWebView.postMessage:', typeof window.ReactNativeWebView.postMessage);

									// 多次验证确保对象可用
									for (var i = 0; i < 5; i++) {
										(function(index) {
											setTimeout(function() {
												console.log('=== Verification ' + index + ' ===');
												console.log('window.ReactNativeWebView exists:', !!window.ReactNativeWebView);
												console.log('postMessage exists:', !!(window.ReactNativeWebView && window.ReactNativeWebView.postMessage));
												console.log('postMessage type:', typeof (window.ReactNativeWebView && window.ReactNativeWebView.postMessage));

												if (window.ReactNativeWebView && typeof window.ReactNativeWebView.postMessage === 'function') {
													console.log('=== SUCCESS: ReactNativeWebView.postMessage is READY at verification ' + index + ' ===');
												} else {
													console.error('=== FAILED: ReactNativeWebView.postMessage is NOT AVAILABLE at verification ' + index + ' ===');
													// 重新尝试设置
													window.ReactNativeWebView = ReactNativeWebViewObj;
												}
											}, index * 200);
										})(i);
									}

									// 设置一个全局标记，表示兼容层已注入
									window.__REACT_NATIVE_WEBVIEW_INJECTED__ = true;

								})();
							`;

							targetWebview.evalJS(script);
							console.log('Enhanced script injected into webview successfully');
						}
					}
				}
			} catch (error) {
				console.error('Failed to inject webview script:', error);
			}
		}, 100); // 减少延迟，更快注入
	}
	function handleWebViewMessage(event) {
		//event = {"defaultPrevented":false,
		// "timeStamp":1733106815148,"_stop":false,"_end":false,
		// "type":"onMessage","bubbles":false,"cancelable":false,"target":{},
		// "currentTarget":{},
		//"detail":{"data":[{"type":"scanbarcode","data":"1"}]}}
		//可能用到的参数
		// obj = {
		//             type: "showrighttext",
		// "type": "readdata",
		//  "key": "serverip",
		//				message:[],
		//             state: 0,
		//             right_url: "",
		//             callfunc: callback,
		//             right_text: text,
		// 			title: title,
		// 			url: url,
		// 			value: value,
		// 			valuetype: "1",
		//         }
		console.log('======handleWebViewMessage from h5=========', JSON.stringify(event))
		//let params = event.detail.data[0]
		   let params;
			// 处理不同格式的消息数据
			if (event.detail && event.detail.data) {
				params = event.detail.data[0];
			} else if (event.detail) {
				// 直接从 detail 获取数据
				try {
					params = typeof event.detail === 'string' ? JSON.parse(event.detail) : event.detail;
				} catch (e) {
					console.error('Failed to parse message data:', e);
					return;
				}
			} else {
				console.error('Invalid message format:', event);
				return;
			}
		let actionType = params.type
		switch (actionType) {

			// 隐藏已经显示的软键盘，如果软键盘没有显示则不做任何操作
			case "hideKeyboard":
				uni.hideKeyboard()
				break;
				//扫描二维码 设备
			case "scanbarcode":
				// {  
				//   "type": "scanbarcode",
				//    key: indexData
				//  } 
				scanbarcode(params)
				break;

				//显示导航栏二级页面通过壳打开	
			case "showrighttext":
				// {
				//           type: "showrighttext",
				//           state: 0,
				//           right_url: "",
				//           callfunc: callback,
				//           right_text: text,
				//       }
				showrighttext(params)
				break;
			case "goToWeb2":
				// {
				//           type: "goToWeb2",
				//           title: title,
				//           url: url,
				//       }
				goToWeb2(params)
				break;
				/**
				 * 跳转到三级页面（如：详细记录）
				 * @param {*} title 页面标题
				 * @param {*} url 页面路径
				 */
			case "goToWeb3":
				// {
				//             type: "goToWeb3",
				//             title: title,
				//             url: url,
				//         }
				goToWeb3(params)
				break;
				/**
				 * 向本地写入数据（数据持久化）
				 * @param {string} key 数据存储的key值
				 * @param {string} value 数据
				 */
			case "writedata":
				// {
				//           type: "writedata",
				//           key: key,
				//           value: value,
				//           valuetype: "1",
				//       }
				writedata(params)
				break;
			case "readdata":
				//读取APP壳的HttpHeader信息
				// {
				//     "type": "readdata",
				//     "key": "HttpHeader",
				//     "types": 1,
				//     "backfunc": "Readinfor"
				// };
				readdata(params)
				break;
				//设置横屏
			case "lockToLandscape":
				// {
				//             type: 'lockToLandscape'
				//         }
				lockToLandscape(params)
				break;
				/**
				 * PDA页面加载自动聚焦到第一个
				 * 输入框，在调用this.xxInput.focus()
				 * 前需要先调用这个方法
				 * callback 回调方法
				 */
				// case "requestFocus":
				// 	// {
				// 	//             type: "requestFocus",
				// 	//         }
				// 	requestFocus(params)
				// 	break;
				/**
				 * 返回上一页
				 */
			case "goBack":
				// {
				//             type: "goBack"
				//         }
				navigateBack(params)
				break;
				/**
				 * 刷新前一个页面
				 */
			case "refreshFront":
				// {
				//            type: "refreshFront"
				//        }
				refreshFront(params)
				break;
				/**
				 * 推送消息（走APP推送）
				 * @param {*} message 消息体
				 */
			case "PushMessage":
				// {
				//             type: "PushMessage",
				//             message
				//         }
				PushMessage(params)
				break;
				/**
				 * 推送企业微信消息
				 * @param {*} touser 推送用户 
				 * @param {*} message 消息内容
				 */
			case "text":
				//{
				//     touser: touser,
				//     msgtype: "text",
				//     content: message
				// }
				PushText(params)
				break;
			case "NFC":
				// {  
				//  "type": "NFC",
				//   key: indexData // 其它参数，可能多个NFC功能
				//   } 
				NFC_fn(params)
				break;
			default:
				break;
		}
	}
	// 跳转到三级页面（如：详细记录）
	function goToWeb3(fromParams) {
		let params = {
			title: fromParams.title,
			url: fromParams.url
		}
		uni.navigateTo({
			url: '/pages/common/thirdPage',
			success: function(res) {
				// 通过eventChannel向被打开页面传送数据
				res.eventChannel.emit('acceptDataFromsecondPage', params)
			}
		})
	}
	// 设置横屏
	function lockToLandscape() {
		//plus.screen.unlockOrientation()
		console.log("====lockToLandscape start====")
		plus.screen.lockOrientation('landscape - primary');
		console.log("====lockToLandscape end====")
	}
	// 启用NFC功能
	function NFC_fn(params) {
		//let _self = this
		// 无论是否能读取，10秒后停止读取
		setTimeout(function() {
			closeNFC();
			clearInterval(NFCTimer.value)
			uni.hideLoading();
		}, 10000);
		setNfcType('read') // 设置读取类型 ['cardNo', 'write', 'read']
		// 初始化 NFC，并且立即开启读取NFC功能
		initNFC()
		uni.showLoading({
			title: 'NFC 读取中...'
		});
		setIntervalNFC(params)
	}
	// 每秒循环读取NFC内容一次 ，如果读取到，立刻停止读取循环
	function setIntervalNFC(event) {
		//let _self = this
		
		NFCTimer.value = setInterval(() => {
		
			let readNFCText = getNfcText()
			if (readNFCText) {
				// uni.showToast({
				// 	title: 'sending message...',
				// 	icon: 'none'
				// })
				let params = {
					data: readNFCText,
					params: event
				}
				responseDataToWebView(params)
				closeNFC();
				clearInterval(NFCTimer.value)
				uni.hideLoading();
			}
		}, 1000)
	}
	// 推送企业微信消息
	function PushText() {

	}
	// 推送消息（走APP推送）
	function PushMessage(params) {
		// 设置角标
		setTimeout(() => {
			uni.showTabBarRedDot({
				index: 3,
				//  text: '10' // 不支持
			})
		}, 1000)
		// uni.hideTabBarRedDot(
		// 		{
		// 				  index: 3,
		// 				//  text: '10' // 不支持
		// 			}
		// )
	}

	function writedata(params) {
		// {
		//           type: "writedata",
		//           key: key,
		//           value: value,
		//           valuetype: "1",
		//       }
		try {
			uni.setStorageSync(params.key, params.value);
		} catch (e) {
			console.error('写入APP壳的writedata信息 错误，key:', params.key)
		}
	}

	//读取APP壳的HttpHeader信息
	function readdata(params) {
		// {
		//     "type": "readdata",
		//     "key": "HttpHeader",
		//     "types": 1,
		//     "backfunc": "Readinfor"
		// }
		try {
			const value = uni.getStorageSync(params.key);
			if (value) {
				console.log('读取APP壳的readdata信息:', value);
				// let params = {
				// 				data: value,
				// 				params: 'Readinfor'
				// 			}
				// responseDataToWebView(params)
				  let responseParams = {
						data: value,
						params: params.backfunc || 'Readinfor'
				}
            responseDataToWebView(responseParams)
			}
		} catch (e) {
			console.error('读取APP壳的readdata信息 错误，key:', params.key)
		}
	}
	// 刷新前一个页面
	function refreshFront(params) {
		//重新加载 web-view 组件当前页面
		reloadWebViewUrl.value = webViewUrl.value
		webViewUrl.value = ""
		setTimeout(()=>{
			webViewUrl.value =reloadWebViewUrl.value
		},1000)
	}

// 执行WEBVIEW回调函数 - 增强版本
	function responseDataToWebView(data, fnName = '') {
		console.log("responseDataToWebView：", JSON.stringify(data))
		
		// 新增：兼容 React 版本的回调方式
		if (data.params && typeof data.params === 'string') {
			// 根据回调函数名调用对应的全局函数
			const callbackName = data.params;
			if (window[callbackName] && typeof window[callbackName] === 'function') {
				window[callbackName](data.data);
			}
		}else{
			// 原有的 uni-app 方式
			const _pages = getCurrentPages();
			const _currentPage = _pages[_pages.length - 1];
			const _funName = !!fnName ? fnName : 'messageFromUniApp'
			const _data = { data: data };
			const currentWebview = _currentPage.$getAppWebview().children()[0];
			currentWebview.evalJS(`${_funName}(${JSON.stringify(_data)})`);
		}
		
	}


	function toggleMenu(type) {
		uni.showActionSheet({
			itemList: ['全部已读', '全部删除'],
			success: function(res) {
				console.log('选中了第' + (res.tapIndex + 1) + '个按钮');
			},
			fail: function(res) {
				console.log(res.errMsg);
			}
		});
	}
	/////////////////////调用APP壳的功能方法/////////////////////
	function showrighttext(params) {
		console.log("=set showrighttext=======", JSON.stringify(params))
		//           right_url: "",
		//           callfunc: callback,
		//           right_text: text,
		rightText.value = params.right_text
		rightUrl.value = params.right_url
	}
	//开启二维码读取功能
	function scanbarcode(fromParams) {
		uni.scanCode({
			success: (res) => {
				let params = {
					data: res.result,
					params: fromParams
				}
				responseDataToWebView(params)
			},
			fail: (err) => {
				console.log('扫描失败', err)
			}
		})
	}
	// 返回上一个页面
	function navigateBack() {
		uni.navigateBack()
	}
	// 设置标题
	function setTitle(title) {
		title.value = title
		if (!title) {
			// 如果为空，保留首次APP进入的标题
			title.value = defaultPageTitle.value
		}
		// uni.setNavigationBarTitle({
		// 	title: title.value,
		// 	success: function() {
		// 		console.log('Navigation bar title set successfully.');
		// 	},
		// 	fail: function(err) {
		// 		console.error('Failed to set navigation bar title.', err);
		// 	}
		// });
	}

	function goToWeb2(fromParams) {
		let params = {
			title: fromParams.title,
			url: fromParams.url
		}
		uni.navigateTo({
			url: '/pages/common/secondPage',
			success: function(res) {
				// 通过eventChannel向被打开页面传送数据
				res.eventChannel.emit('acceptDataFromCommonPage', params)
			}
		})
	}
	// 顶部-右侧按钮调整功能
	function navigateToSecondPage() {
		// 跳转到二级自定义WEBVIEW页面，如历史记录
		let params = {
			title: rightText.value,
			url: rightUrl.value
		}
		uni.navigateTo({
			url: '/pages/common/secondPage',
			success: function(res) {
				// 通过eventChannel向被打开页面传送数据
				res.eventChannel.emit('acceptDataFromCommonPage', params)
			}
		})
	}

	function navigateTo(fromParams) {
		//在起始页面跳转到test.vue页面并传递参数
		uni.navigateTo({
			url: fromParams.url, //'test?id=1&name=uniapp'
			success: function(res) {
				setTitle(fromParams.title)
			},
			fail: function(res) {
				setTitle("")
			}
		});
	}
</script>

<style>

</style>