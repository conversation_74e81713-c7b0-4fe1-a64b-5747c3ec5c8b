<template>
	<view class="static">
		<!-- left 
		<uni-nav-bar :border="false" background-color="#2F6FFE" color="#fff" :fixed="true" status-bar>
			<view class="flex fontSize-32 fontWeight-bold w-full  justify-center items-center">我的</view>
		</uni-nav-bar>-->
		<view class="header-section sticky zIndex9 top-0 left-0">
			<view class="flex">
				<view class="flex-1 flex flex-col ">
					<!-- left -->
					<view class="flex">
						<!-- inner left -->
						<view style="width: 65px; height: 65px;" class="bg-image-userInfo">
						</view>
						<view style="margin-left: 10px;" class="flex justify-center items-center flex-col">
							<!-- inner right -->
							<view class="flex">
								<!-- up -->
								用户名称
							</view>
							<view class="flex">
								<!-- 	down -->
								用户编码
							</view>
						</view>
					</view>

				</view>
				<view @click="showSeviceQRCode=true" style="width: 30%;" class="flex justify-end  items-center ">
					<!-- right -->
					<!-- 二维码 -->
					<image style="width: 60rpx;height: 60rpx;" :src="'/static/svg/qrcode.svg'" mode="aspectFit"></image>
					<!-- <uni-icons style="margin-left: 10px;" type="forward" color="#000" size="16"></uni-icons> -->
				</view>

			</view>
		</view>
		<view style="margin-top:20rpx;" class="section-list pl-20 pr-20 flex flex-col ">

			<view @click="isShowRecordList=!isShowRecordList" class="section-item justify-between ">
				<view class="flex w-full items-center">
					<uni-icons type="auth" color="#000" size="32"></uni-icons>
					用户记录
				</view>
				<view @click="navigateTo('/pages/mine/password')" class="flex  items-center">
					<block v-if="isShowRecordList">
						<uni-icons type="up" color="#000" size="16"></uni-icons>
					</block>
					<block v-else>
						<uni-icons type="down" color="#000" size="16"></uni-icons>
					</block>
				</view>
			</view>
			<view v-show="isShowRecordList" @click="navigateTo('/pages/mine/log-service')"
				class="section-item justify-between ">
				<view class="flex w-full items-center">
					<uni-icons type="info" color="#000" size="32"></uni-icons>
					服务日志
				</view>
				<view @click="navigateTo('/pages/mine/log-service')" class="flex  items-center">
					<uni-icons type="forward" color="#000" size="16"></uni-icons>
				</view>
			</view>
			<view v-show="isShowRecordList" @click="navigateTo('/pages/mine/log-debgger')"
				class="section-item justify-between ">
				<view class="flex w-full items-center">
					<uni-icons type="info" color="#000" size="32"></uni-icons>
					调试日志
				</view>
				<view @click="navigateTo('/pages/mine/log-debgger')" class="flex  items-center">
					<uni-icons type="forward" color="#000" size="16"></uni-icons>
				</view>
			</view>

			<view v-show="isShowRecordList" @click="navigateTo('/pages/mine/log-login')"
				class="section-item justify-between ">
				<view class="flex w-full items-center">
					<uni-icons type="info" color="#000" size="32"></uni-icons>
					登录日志
				</view>
				<view @click="navigateTo('/pages/mine/log-login')" class="flex  items-center">
					<uni-icons type="forward" color="#000" size="16"></uni-icons>
				</view>
			</view>
			<view @click="navigateTo('/pages/mine/password')" class="section-item justify-between ">
				<view class="flex w-full items-center">
					<uni-icons type="locked" color="#000" size="32"></uni-icons>
					修改密码
				</view>
				<view @click="navigateTo('/pages/mine/password')" class="flex  items-center">
					<uni-icons type="forward" color="#000" size="16"></uni-icons>
				</view>
			</view>
			<view @click="navigateTo('/pages/mine/aboutPhone')" class="section-item justify-between ">
				<view class="flex w-full items-center">
					<uni-icons type="gear" color="#000" size="32"></uni-icons>
					本机信息
				</view>
				<view @click="navigateTo('/pages/mine/aboutPhone')" class="flex  items-center">
					<uni-icons type="forward" color="#000" size="16"></uni-icons>
				</view>
			</view>
			<view class="section-item justify-between ">
				<view class="flex items-center">
					<uni-icons type="info" color="#000" size="32"></uni-icons>
					推送消息
				</view>
				<view class="flex items-center">
					<switch color="#4dd865" checked @change="switch1Change" />
				</view>
			</view>
			<view class="section-item justify-between ">
				<view @click="navigateTo('/pages/mine/about')" class="flex  w-full items-center">
					<uni-icons type="staff" color="#000" size="32"></uni-icons>
					关于APP
				</view>
				<view @click="navigateTo('/pages/mine/about')" class="flex">
					<uni-icons type="forward" color="#000" size="16"></uni-icons>
				</view>
			</view>
			<!-- <view class="section-item justify-between ">
				<view @click="navigateTo('/pages/blueTeeth/blueTeeth')" class="flex  w-full items-center">
					<uni-icons type="settings" color="#000" size="32"></uni-icons>
					蓝牙测试
				</view>
				<view @click="navigateTo('/pages/blueTeeth/blueTeeth')" class="flex">
					<uni-icons type="forward" color="#000" size="16"></uni-icons>
				</view>
			</view>-->
			<view class="section-item justify-between ">
				<view @click="navigateTo('/pages/locationCheckIn/locationCheckIn')" class="flex  w-full items-center">
					<uni-icons type="location" color="#000" size="32"></uni-icons>
					定位打卡
				</view>
				<view @click="navigateTo('/pages/locationCheckIn/locationCheckIn')" class="flex">
					<uni-icons type="forward" color="#000" size="16"></uni-icons>
				</view>
			</view> 
			<view class="section-item justify-between ">
				<view @click="navigateTo('/pages/mqttToolTest/mqttToolTest')" class="flex  w-full items-center">
					<uni-icons type="location" color="#000" size="32"></uni-icons>
					mqtt消息推送
				</view>
				<view @click="navigateTo('/pages/mqttToolTest/mqttToolTest')" class="flex">
					<uni-icons type="forward" color="#000" size="16"></uni-icons>
				</view>
			</view> 
		</view>

		<view class="sticky zIndex9 bottom-0 mt-20 mb-20">
			<button @click="reLaunch('/pages/login/login')" class="mb-20"
				style="width: 100%;background-color: #fff;color: orange;">退出登录</button>
		</view>
		<!-- 输入框示例 -->
		<uni-popup ref="passwordDialog" type="dialog">
			<uni-popup-dialog ref="inputClose" mode="input" title="修改密码" value="" placeholder="请输入内容"
				@confirm="dialogInputConfirm"></uni-popup-dialog>
		</uni-popup>
		<u-popup mode='center' v-model:show="showSeviceQRCode">
			<view class="flex justify-center items-center p-all-40 flex-col">
				<view class="flex mb-20">
					<text>服务二维码</text>
				</view>
				<view class="flex">
					<u-qrcode v-if="showSeviceQRCode" :size="200" val="http://************:6682"></u-qrcode>
				</view>
				<!-- 	<view class="flex mt-20">
						<text>中洛PCB外网[************:6682]</text>
					</view> -->
				<view class="flex mt-20">

					<button @click="copyInfo()" style="width: 200rpx;background-color: #20273d;"
						type="primary">复制</button>
				</view>
			</view>
		</u-popup>
		<!-- 提示信息弹窗 -->
		<uni-popup ref="messagePopup" type="message">
			<uni-popup-message :type="messageConfig.msgType" :message="messageConfig.messageText"
				:duration="2000"></uni-popup-message>
		</uni-popup>
	</view>
</template>
<script setup>
	import {
		ref,
		reactive
	} from 'vue'
	import {
		setStorageSync,
		getStorageSync,
		CURRENT_SERVER
	} from '@/utils/Storage.js'
	const isShowRecordList = ref(false)
	const imgMode = ref('widthFix') //宽度不变，高度自动变化，保持原图宽高比不变
	const avatar = ref('./static/images/logo/mrtx.png') //头像
	const passwordDialog = ref(null)
	const currentUserInfo= ref({})
	const showSeviceQRCode = ref(false) // 是否显示二维码
	const messagePopup = ref(null)
	const messageConfig = reactive({
		msgType: 'success',
		messageText: ''
	})
	///////////////////////methods/////////////////////////
	function getAndSetUserInfo(){
		let _USER_INFO = getStorageSync('USER_INFO')
		
		if(_USER_INFO){
			let userInfoObj = JSON.parse(_USER_INFO)
			//debugger
		}
	}
	function messageToggle(messageText = '', msgType = 'success') {
		messageConfig.messageText = messageText
		messageConfig.msgType = msgType
		messagePopup.value.open()
	}

	function copyInfo() {
		showSeviceQRCode.value = false
		uni.setClipboardData({
			data: '1234567890',
			//showToast: false,
			success: function() {
				//messageToggle('复制成功')
			}
		});
	}

	function dialogInputConfirm() {
		uni.showToast({
			title: '修改成功！',
			duration: 2000
		});

	}

	function openPopup() {

		passwordDialog.value.open()
	}

	function closePopup() {
		passwordDialog.value.close()
	}
	// 关闭所有页面，打开到应用内的某个页面。
	function reLaunch(url) {
		uni.reLaunch({
			url: url
		})
	}
	//保留当前页面，跳转到应用内的某个页面，使用uni.navigateBack可以返回到原页面。
	function navigateTo(url) {
		uni.navigateTo({
			url: url
		});
	}
	/////////////////////methods/////////////////////////		
</script>
<style lang="scss">
	// .diffH5-top{
	// 	// #ifdef H5
	// 		//margin-top: 55px;
	// 	// #endif
	// }

	.mine-container {
		background-color: #ebebeb;
	}

	.header-section {
		border-top: 1px solid #ccc;
		padding: 15px 15px 45px 15px;
		background-color: #fff;
		color: #000;
		// .login-tip {
		//      font-size: 18px;
		//      margin-left: 10px;
		//    }

		.cu-avatar {
			border: 2px solid #eaeaea;

			.icon {
				font-size: 40px;
			}
		}
	}

	.section-list {
		background-color: #fff;
	}

	.section-item {
		display: flex;
		align-items: center;
		border-bottom: 1px solid #e9e9e9;
		height: 100rpx;
		//margin-bottom: 10px;
		padding-left: 20rpx;
	}
</style>