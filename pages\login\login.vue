<template>
	<view class="h-screen flex flex-col  justify-between">

		<view class="flex">
			<!-- top -->
		</view>
		<view class="flex-1 flex flex-col justify-between items-center bg-image-bg">
			<!-- content -->
			<view style="width:670rpx; " class="flex h-full flex-col justify-center">
				<view style="height: 350rpx; " class="flex  flex-col justify-end">
					<view style="height: 128rpx;" class="">
						<image style="width: 108rpx;height: 108rpx;" :src="loginPageInfo.APP_IMG_Url" mode="aspectFit">
						</image>
					</view>
					<view style="height: 128rpx;font-size: 32rpx;" class="flex items-center">
						<view style="font-size: 50rpx;font-weight: bold;">
							<view v-text="loginPageInfo.APP_Text"></view>
						</view>
					</view>
				</view>

				<view class="flex-1 border-red1">
					<!-- 登录表单 login form-->
					<view style="max-height: 800rpx;" class="h-full border-blue1 flex flex-col justify-center ">

						<view v-if="loginPageInfo.showSelectCompanyList" class="flex items-center login-input-item1">
							<view class="iconfont icon-code icon " style="color: #808080;font-size: 60rpx;"></view>
							<uni-easyinput ref="companyIdRef" @focus="toggleMenu()" @clear="clear_companyName" v-model="loginForm.companyName"
								primaryColor="#20273d" placeholderStyle="fontSize:28rpx;"
								:styles="{ 'backgroundColor': '#fff' }" :inputBorder="false"
								:placeholder="loginPageInfo.companyIdText"></uni-easyinput>
						</view>
						<view class="flex items-center login-input-item1">
							<view class="iconfont icon-user icon " style="color: #808080;font-size: 60rpx;"></view>
							<uni-easyinput ref="userNameRef" primaryColor="#20273d" v-model="loginForm.userName"
								placeholderStyle="fontSize:28rpx;"
								:styles="{ 'backgroundColor': '#fff', 'width': '90%' }" :inputBorder="false"
								:placeholder="loginPageInfo.userNameText"></uni-easyinput>
						</view>
						<view class="flex items-center login-input-item1">
							<view class="iconfont icon-password icon" style="color: #808080;font-size: 60rpx;"></view>
							<uni-easyinput primaryColor="#20273d" v-model="loginForm.userPassword"
								placeholderStyle="fontSize:28rpx;" :styles="{ 'backgroundColor': '#fff' }"
								type="password" :inputBorder="false"
								:placeholder="loginPageInfo.userPasswordText"></uni-easyinput>
						</view>

						<view style="margin-top: 50rpx;" class="flex  ">
							<button @click="loginFn()" :loading="isLogin_loading"
								style="width: 100%;background-color: #20273d;" type="primary">
								{{ loginPageInfo.loginBtnText }}
							</button>
						</view>
						<view style="margin-top: 10px;font-size: 28rpx;" class="flex">
							<view v-if="loginPageInfo.showRemember_userName" style="width:240rpx;overflow: hidden;"
								class="">
								<checkbox-group @change="change_keepData('user')">
									<label>
										<checkbox style="transform:scale(0.7)" backgroundColor="#fff"
											borderColor="#f5af1d" activeBorderColor="#f5af1d"
											activeBackgroundColor="#f5af1d" color="#fff" value="user"
											:checked="keepData_user" />
										<text style="margin-left: 6rpx; color: #1d2640">记住用户</text>
									</label>
								</checkbox-group>
							</view>
							<view v-if="loginPageInfo.showRemember_userPassword" style="width:240rpx;overflow: hidden;"
								class="">
								<checkbox-group @change="change_keepData('password')">
									<label>
										<checkbox style="transform:scale(0.7)" backgroundColor="#fff"
											borderColor="#f5af1d" activeBorderColor="#f5af1d"
											activeBackgroundColor="#f5af1d" color="#fff" value="password"
											:checked="keepData_password" />
										<text style="margin-left: 6rpx; color: #1d2640">记住密码</text>
									</label>
								</checkbox-group>
							</view>
							<view v-if="loginPageInfo.showRemember_companyId" style="width:260rpx;overflow: hidden;"
								class="">
								<checkbox-group @change="change_keepData('companyId')">
									<label>
										<checkbox style="transform:scale(0.7)" backgroundColor="#fff"
											borderColor="#f5af1d" activeBorderColor="#f5af1d"
											activeBackgroundColor="#f5af1d" color="#fff" value="companyId"
											:checked="keepData_companyId" />
										<text style="margin-left: 6rpx; color: #1d2640">记住企业ID</text>
									</label>
								
								</checkbox-group>
							</view>

						</view>
						<view style="margin-top: 10px;color: orange;font-size: 26rpx;"
							class="flex justify-center items-center flex-nowrap  ">
							<!-- 中洛PCB外网[218.95.67.64:6682] -->
							{{ currentServerInfo.CSERVER_NAME }}
						</view>
						<view class="flex mt-20 justify-center items-center">
							<!-- style="border:1px solid #20273d;padding: 10rpx 30rpx;border-radius: 14px;" -->
							<view @click="navigateTo('/pages/servers/servers')"
								class="flex justify-center items-center">
								<view class="flex ">
									<uni-icons type="gear-filled" size="20"></uni-icons>
								</view>
								<view style="font-size: 28rpx;" class="flex">
									服务器配置
								</view>
							</view>
						</view>
					</view>

					<!-- 登录表单 login form -->
				</view>
				<view style="height: 60rpx;" class="flex flex-col justify-between items-center ">
					<view style="font-size: 26rpx;color: #909399;" class="flex">
						<view class="" v-text="loginPageInfo.copyRightText"></view>
					</view>
				</view>
			</view>

		</view>
		<view class="flex">
			<!-- 	bottom -->
		</view>
		<!-- 普通弹窗 -->
		<uni-popup ref="popup" :mask-click="true" type="bottom" border-radius="10px 10px 0 0">
			<text>Popup</text>
			<button @click="close">关闭</button>
		</uni-popup>
	</view>
</template>

<script setup>
	import {
		ref,
		reactive,
		onMounted
	} from 'vue'
	import {
		setStorageSync,
		getStorageSync,
		CURRENT_SERVER
	} from '@/utils/Storage.js'
	import * as serviceApi from '@/api/index.js'
	import {
		md5
	} from '@/utils/md5.js'
	const popup = ref(null)
	const isLogin_loading = ref(false)
	const companyIdRef = ref(null)
	const userNameRef = ref(null)
	const keepData_user = ref(true)
	const keepData_password = ref(false)
	const keepData_companyId = ref(false)

	const loginForm = reactive({
		userName: '',
		userPassword: '',
		companyId: '',
		companyName:'',
	})
	const currentServerInfo = ref({
		CSERVER_NAME: ''
	})
	const selectCompany = ref(null)
	const companyType = ref([
		// {
		// 	value: 100,
		// 	text: "中软"
		// },
		// {
		// 	value: 101,
		// 	text: "超毅"
		// },
		// {
		// 	value: 102,
		// 	text: "运丰"
		// },
	])
	const checkList = ref([{
		text: '记住我',
		value: 0
	}])
	const loginPageInfo = reactive({
		APP_IMG_Url: '/static/images/bg/logo.png',
		APP_Text: '欢迎登录',
		loginBtnText: '登录',
		userNameText: '用户',
		userPasswordText: '密码',
		companyIdText: '企业ID',
		showSelectCompanyList:true,
		copyRightText: 'Copyright@2018-2025 Csoft:CI.MOM',
		showRemember_userName: true,
		showRemember_userPassword: true,
		showRemember_companyId: true
	})
	onMounted(() => {
		loginPageInfo.loginBtnText = '登录'
		// 设置默认值 必须
		setStorageSync('KEEP_LOING_USER', !!keepData_user.value)
		readDataFromKeep()
		getServerData()
		loadCompanyList()
		setLoginPageConfig()
	})
	//////////////////////methods//////////////////////////
	function setLoginPageConfig(){
		let _LOGIN_CONFIG = getStorageSync('LOGIN_CONFIG')
		
		if(_LOGIN_CONFIG && _LOGIN_CONFIG.HomeConfig){
			let _HomeConfig = JSON.parse(_LOGIN_CONFIG.HomeConfig)
			//debugger
		}
	}
	
	function clear_companyName(){
		loginForm.companyName = ''
		loginForm.companyId = ''
	}
	// user password companyId
	function change_keepData(name) {
		switch (name) {
			case 'user':
				keepData_user.value = !keepData_user.value
				break;
			case 'password':
				keepData_password.value = !keepData_password.value
				break;
			case 'companyId':
				keepData_companyId.value = !keepData_companyId.value
				break;
			default:
				break;
		}
	}

	function toggleMenu(type) {
		if(companyType.value.length==0 || companyType.value == [] || companyType.value == null) {
			loadCompanyList()
		}
		let dataList = formatCompanyList()
		uni.showActionSheet({
			itemList: dataList,
			success: function(res) {
				console.log('选中了第' + (res.tapIndex + 1) + '个按钮')
				//debugger
				loginForm.companyId = companyType.value[res.tapIndex].CID
				loginForm.companyName = companyType.value[res.tapIndex].CENTERPRISE_NAME
				setTimeout(() => {
					userNameRef.value.focusShow = false
					userNameRef.value.focused = false
					userNameRef.value.onFocus()
				}, 300)
			},
			fail: function(res) {
				// 取消 触发 聚焦 账号输入框
				userNameRef.value.focusShow = false
				userNameRef.value.focused = false
				userNameRef.value.onFocus()
				console.log(res.errMsg);
			}
		});
	}

	function getServerData() {
		currentServerInfo.value = {
			CSERVER_NAME: ''
		}
		let data = getStorageSync(CURRENT_SERVER)
		if (data && data.CSERVER_NAME) {
			currentServerInfo.value = data
		}
	}
	function formatCompanyList(){
		let list =[]
		companyType.value.forEach(item => {
			list.push(item.CENTERPRISE_NAME)
		})
		return list
	}

	function loadCompanyList(){
		companyType.value =[]
		const params = {
			//systemType=WEB_CONFIG&language=zh
			systemType:'APP',
			language:'zh'
		}
		 		//   "CENTERPRISE_NO": "RY",
                //     "CENTERPRISE_NAME": "软云科技",
                //     "CFULL_NAME": "江苏软云科技有限公司",
                //     "CADDRESS": "",
                //     "CREMARK": null,
                //     "CAPI_PRE": "RY",
                //     "IS_READONLY": true,
                //     "IS_UPDATE": false,
                //     "CID": "120",
                //     "CUSER_CREATED": "SYS",
                //     "CDATETIME_CREATED": "2023-01-30 18:28:51",
                //     "CUSER_MODIFIED": "SYS",
                //     "CDATETIME_MODIFIED": "2023-01-30 18:28:51",
                //     "CSTATE": "A",
                //     "CSTATE_BOOL": true,
                //     "CINSTANCE_ID": "",
                //     "CROWREMARK": "",
                //     "CENTERPRISE_CODE": "0",
                //     "CORG_CODE": "0"
		serviceApi.GetCompanyList(null,params).then(res => {
			if (res && res.data.code === 200 && res.data.data) {
				//setStorageSync('Enterprises', res.data.data.Datas.Enterprises)
				companyType.value = res.data.data.Datas.Enterprises
			} else {
				uni.showToast({
					title: res && res.data.data.Content ? res.data.data.Content : '获取企业列表失败',
					icon: 'none'
				})
			}
		}).catch(err => {
			uni.showToast({
				title: '服务异常，请检查配置!',
				icon: 'none'
			})
		})
	}
	function LOAD_DATA(){
		const params = {
		}
		serviceApi.APINAME(params).then(res => {
			if (res && res.data.code === 200 && res.data.data) {
				
			} else {
				uni.showToast({
					title: res && res.data.data.Content ? res.data.data.Content : '获取数据失败',
					icon: 'none'
				})
			}
		}).catch(err => {
			uni.showToast({
				title: '服务异常，请检查配置!',
				icon: 'none'
			})
		})
	}
	function loginFn() {
		if(!currentServerInfo.value.CSERVER_NAME){
			uni.showToast({
				title: '请先选择服务',
				icon: 'none'
			})
			return
		}
		// 必填输入
		if(loginPageInfo.showSelectCompanyList){
			if (!loginForm.companyName) {
				uni.showToast({
					title: '请输入/选择企业',
					icon: 'none'
				})
				return
			}
		}
		
		// 必填输入
		if (!loginForm.userName) {
			uni.showToast({
				title: '请输入账号',
				icon: 'none'
			})
			return

		}
		if (!loginForm.userPassword) {
			uni.showToast({
				title: '请输入密码',
				icon: 'none'
			})
			return
		}
		isLogin_loading.value = true
		loginPageInfo.loginBtnText = '登录中...'
		// 是否保存记住信息
		keepDataInApp()
		// 构造登录参数
		const params = {
			UserName: loginForm.userName,
			Password: md5(loginForm.userPassword),
			Enterprise: loginForm.companyId || 100,
			LoginType: 'APP'

		}

		serviceApi.login(params).then(res => {
			//debugger
			const token = res.header['token']; // 这里用小写是因为axios会自动将头名称转为小写
			if(token){
				console.log('登录成功，获取TOKEN:',token)
				setStorageSync('token', token)
			}
			
			isLogin_loading.value = false
			loginPageInfo.loginBtnText = '登录'
			//debugger
			if (res && res.data.code === 200 && res.data.data.Success) {
				// 登录成功，保存用户信息、权限菜单等
				setStorageSync('USER_INFO', res.data.data.Datas.UserInfo)
				setStorageSync('ModuleData', res.data.data.Datas.Modules)
				// 动态设置底部标签是否显示
				let ModuleTrees = res.data.data.Datas.Modules.ModuleTrees
				if(ModuleTrees){
						let showHomeIndex = ModuleTrees.findIndex(item=>item.CBEHAVIOR_PATH=='Home')
						let showWorkIndex = ModuleTrees.findIndex(item=>item.CBEHAVIOR_PATH=='Work')
						let showMessageIndex = ModuleTrees.findIndex(item=>item.CBEHAVIOR_PATH=='Message')
						if(showHomeIndex>-1){
							// 跳转首页
							reLaunch('/pages/home/<USER>')
						}else{
							//跳转应用菜单
							reLaunch('/pages/menus/menus')
							
							setTimeout(()=>{
								uni.setTabBarItem({
								index:0,
								visible:false,
									success: function () {
										// uni.showToast({
										// 	title:'动态设置TabBar成功！'
										// })
									},
									fail: function () {
										uni.showToast({
											title:'动态设置TabBar失败！'
										})
									},
								})
								
								if(showWorkIndex==-1){
										uni.setTabBarItem({
										index:2,
										visible:false,
										})
								}
								if(showMessageIndex==-1){
										uni.setTabBarItem({
										index:3,
										visible:false,
										})
								}
							},1000)
							
						}
						
				}
			
			} else {
				uni.showToast({
					title: res && res.data.data.Content ? res.data.data.Content : '登录失败',
					icon: 'none'
				})
			}
		}).catch(err => {
			isLogin_loading.value = false
			loginPageInfo.loginBtnText = '登录'
			uni.showToast({
				title: '服务异常，请检查配置!',
				icon: 'none'
			})
		})
	}

	// 是否记住用户，记住密码，记住企业ID
	function keepDataInApp() {
		setStorageSync('KEEP_LOING_USER', !!keepData_user.value)
		setStorageSync('KEEP_LOING_PASSWORD', !!keepData_password.value)
		setStorageSync('KEEP_LOING_COMPANYID', !!keepData_companyId.value)
		

		if (!!keepData_user.value) {
			setStorageSync('LOING_USER', loginForm.userName)
		}
		if (!!keepData_password.value) {
			setStorageSync('LOING_PASSWORD', loginForm.userPassword)
		}
		if (!!keepData_companyId.value) {
			setStorageSync('LOING_COMPANYID', loginForm.companyId)
			setStorageSync('LOING_COMPANYNAME', loginForm.companyName)
		}
	}
	// 读取记住信息
	function readDataFromKeep() {
		//debugger
		
		keepData_user.value = getStorageSync('KEEP_LOING_USER')
		keepData_password.value = getStorageSync('KEEP_LOING_PASSWORD')
		keepData_companyId.value = getStorageSync('KEEP_LOING_COMPANYID')

		if (!!keepData_user.value) {
			let _user = getStorageSync('LOING_USER')
			if (!!_user) {
				loginForm.userName = _user
			}
		}
		if (!!keepData_password.value) {

			let _password = getStorageSync('LOING_PASSWORD')
			if (!!_password) {
				loginForm.userPassword = _password
			}
		}
		if (!!keepData_companyId.value) {
			let _companyId = getStorageSync('LOING_COMPANYID')
			if (!!_companyId) {
				loginForm.companyId = _companyId
			}
			let _companyName = getStorageSync('LOING_COMPANYNAME')
			if (!!_companyName) {
				loginForm.companyName = _companyName
			}

		}
	}

	//  获取app图标并下载到本地
	function getIconsList() {
		serviceApi.icon().then(res => {
			isLogin_loading.value = false
			loginPageInfo.loginBtnText = '登录'
			if (res && res.code === 200) {
				// 保存自定义图标信息
				setStorageSync('ICON_LIST', res.data)
			} else {

			}
		}).catch(err => {
			console.log('获取app图标并下载到本地失败:', err)
		})
	}

	function open() {
		popup.value.open('top')
	}

	function close() {
		popup.value.close()
	}

	function reLaunch(url) {
		uni.reLaunch({
			url: url
		})
	}

	function navigateTo(url) {
		uni.navigateTo({
			url: url
		})
	}
</script>

<style lang="scss" scoped>
	.login-input-item1 {
		padding: 10rpx 0px;
		border-bottom: 1px solid #f1f1f1;

		.icon {
			font-size: 38rpx;
			//margin-left: 10rpx;
			color: #999;
		}

		.input {
			width: 100%;
			font-size: 28rpx;
			line-height: 40rpx;
			text-align: left;
			padding-left: 30rpx;

		}
	}
</style>